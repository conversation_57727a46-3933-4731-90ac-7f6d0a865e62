/**
 * Hook para preview em tempo real de templates
 * Fornece renderização e validação instantânea usando Server Actions
 */

'use client';

import { useState, useCallback, useEffect } from 'react';
import { previewTemplate, renderTemplatePreview } from '@/src/app/(dashboard)/academia/configuracoes/templates/actions/template-preview-actions';
import { validateTemplate } from '@/src/app/(dashboard)/academia/configuracoes/templates/actions/template-actions';
import { TemplateEngine } from '@/services/notifications/templates/template-engine';
import type {
  TemplatePreviewData,
  TemplateValidationResult,
  NotificationType
} from '@/services/notifications/types/notification-types';

interface UseTemplatePreviewReturn {
  // Estado do preview
  previewData: TemplatePreviewData | null;
  validation: TemplateValidationResult | null;
  loading: boolean;
  error: string | null;
  
  // Operações
  generatePreview: (templateId: string, variables: Record<string, any>) => Promise<void>;
  validateLive: (template: string, type: NotificationType) => Promise<void>;
  renderTemplate: (template: string, variables: Record<string, any>) => string;
  
  // Utilitários
  clearError: () => void;
  clearPreview: () => void;
}

export function useTemplatePreview(): UseTemplatePreviewReturn {
  const [previewData, setPreviewData] = useState<TemplatePreviewData | null>(null);
  const [validation, setValidation] = useState<TemplateValidationResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const clearPreview = useCallback(() => {
    setPreviewData(null);
    setValidation(null);
    setError(null);
  }, []);

  const generatePreview = useCallback(async (
    templateId: string,
    variables: Record<string, any>
  ) => {
    setLoading(true);
    setError(null);

    try {
      const result = await previewTemplate(templateId, variables);

      if (result.success) {
        setPreviewData(result.data!);
      } else {
        setError(result.error || 'Erro ao gerar preview');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  }, []);

  const validateLive = useCallback(async (
    template: string,
    type: NotificationType
  ) => {
    setError(null);

    try {
      const result = await validateTemplate(template, type);

      if (result.success) {
        setValidation(result.data!);
      } else {
        setError(result.error || 'Erro ao validar template');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    }
  }, []);

  const renderTemplate = useCallback((
    template: string, 
    variables: Record<string, any>
  ): string => {
    try {
      const result = TemplateEngine.render(template, variables, {
        escapeHtml: true,
        allowMissingVariables: true
      });
      
      return result.rendered;
    } catch (err) {
      console.error('Erro ao renderizar template:', err);
      return template; // Retorna template original em caso de erro
    }
  }, []);

  return {
    // Estado
    previewData,
    validation,
    loading,
    error,
    
    // Operações
    generatePreview,
    validateLive,
    renderTemplate,
    
    // Utilitários
    clearError,
    clearPreview
  };
}

/**
 * Hook para debounce de validação em tempo real
 */
export function useTemplateValidationDebounced(
  template: string,
  type: NotificationType,
  delay: number = 500
): TemplateValidationResult | null {
  const [validation, setValidation] = useState<TemplateValidationResult | null>(null);

  useEffect(() => {
    if (!template.trim() || !type) {
      setValidation(null);
      return;
    }

    const timeoutId = setTimeout(async () => {
      try {
        const result = await validateTemplate(template, type);
        if (result.success) {
          setValidation(result.data!);
        }
      } catch (err) {
        console.error('Erro na validação debounced:', err);
      }
    }, delay);

    return () => clearTimeout(timeoutId);
  }, [template, type, delay]);

  return validation;
}

/**
 * Hook para renderização em tempo real com debounce
 */
export function useTemplateRenderDebounced(
  template: string,
  variables: Record<string, any>,
  delay: number = 300
): string {
  const [rendered, setRendered] = useState('');

  useEffect(() => {
    if (!template.trim()) {
      setRendered('');
      return;
    }

    const timeoutId = setTimeout(() => {
      try {
        const result = TemplateEngine.render(template, variables, {
          escapeHtml: true,
          allowMissingVariables: true
        });
        setRendered(result.rendered);
      } catch (err) {
        console.error('Erro na renderização debounced:', err);
        setRendered(template);
      }
    }, delay);

    return () => clearTimeout(timeoutId);
  }, [template, variables, delay]);

  return rendered;
}
