/**
 * Server Actions para gerenciamento de templates de notificação
 * Substitui a classe TemplateManagementService para compatibilidade com App Router
 */

'use server';

import { createClient } from '@/services/supabase/server';
import { TemplateEngine } from '@/services/notifications/templates/template-engine';
import { TemplateValidator } from '@/services/notifications/templates/template-validator';
import type {
  NotificationTemplate,
  TemplateVariable,
  NotificationType,
  NotificationChannel,
  CreateTemplateData,
  UpdateTemplateData,
  TemplatePreviewData,
  TemplateValidationResult,
  NotificationServiceResponse
} from '@/services/notifications/types/notification-types';
import {
  CreateTemplateSchema,
  UpdateTemplateSchema,
  TemplatePreviewSchema
} from '@/services/notifications/types/notification-schemas';

/**
 * Busca templates por tenant e tipo
 */
export async function getTemplatesByTenant(
  tenantId: string, 
  type?: NotificationType
): Promise<NotificationServiceResponse<NotificationTemplate[]>> {
  try {
    const supabase = await createClient();
    
    let query = supabase
      .from('notification_templates')
      .select('*')
      .or(`tenant_id.eq.${tenantId},tenant_id.is.null`) // Templates do tenant ou globais
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (type) {
      query = query.eq('type', type);
    }

    const { data: templates, error } = await query;

    if (error) {
      console.error('Erro ao buscar templates:', error);
      return {
        success: false,
        error: `Erro ao buscar templates: ${error.message}`
      };
    }

    return {
      success: true,
      data: templates || []
    };

  } catch (error) {
    console.error('Erro no serviço de templates:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Busca um template específico
 */
export async function getTemplate(templateId: string): Promise<NotificationServiceResponse<NotificationTemplate>> {
  try {
    const supabase = await createClient();

    const { data: template, error } = await supabase
      .from('notification_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (error) {
      console.error('Erro ao buscar template:', error);
      return {
        success: false,
        error: `Erro ao buscar template: ${error.message}`
      };
    }

    if (!template) {
      return {
        success: false,
        error: 'Template não encontrado'
      };
    }

    return {
      success: true,
      data: template
    };

  } catch (error) {
    console.error('Erro no serviço de templates:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Cria um novo template
 */
export async function createTemplate(
  tenantId: string, 
  templateData: CreateTemplateData
): Promise<NotificationServiceResponse<NotificationTemplate>> {
  try {
    // Validar dados de entrada
    const validatedData = CreateTemplateSchema.parse(templateData);

    // Validar template antes de criar
    const availableVariables = await getAvailableVariables(validatedData.type);
    if (!availableVariables.success) {
      return {
        success: false,
        error: availableVariables.error || 'Erro ao buscar variáveis disponíveis'
      };
    }

    const validation = await TemplateValidator.validateTemplate(
      validatedData.body_template,
      validatedData.type,
      availableVariables.data!
    );

    if (!validation.isValid) {
      return {
        success: false,
        error: `Template inválido: ${validation.errors.join(', ')}`
      };
    }

    const supabase = await createClient();

    const { data: template, error } = await supabase
      .from('notification_templates')
      .insert({
        tenant_id: tenantId,
        ...validatedData,
        variables: {}, // Será populado automaticamente
        is_active: true,
        is_default: false,
        version: 1
      })
      .select()
      .single();

    if (error) {
      console.error('Erro ao criar template:', error);
      return {
        success: false,
        error: `Erro ao criar template: ${error.message}`
      };
    }

    return {
      success: true,
      data: template,
      message: 'Template criado com sucesso'
    };

  } catch (error) {
    console.error('Erro no serviço de templates:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Atualiza um template existente
 */
export async function updateTemplate(
  templateId: string, 
  updates: UpdateTemplateData
): Promise<NotificationServiceResponse<NotificationTemplate>> {
  try {
    // Validar dados de entrada
    const validatedUpdates = UpdateTemplateSchema.parse(updates);

    // Buscar template atual para validação
    const currentTemplate = await getTemplate(templateId);
    if (!currentTemplate.success) {
      return currentTemplate;
    }

    // Se está atualizando o body_template, validar
    if (validatedUpdates.body_template) {
      const availableVariables = await getAvailableVariables(currentTemplate.data!.type);
      if (!availableVariables.success) {
        return {
          success: false,
          error: availableVariables.error || 'Erro ao buscar variáveis disponíveis'
        };
      }

      const validation = await TemplateValidator.validateTemplate(
        validatedUpdates.body_template,
        currentTemplate.data!.type,
        availableVariables.data!
      );

      if (!validation.isValid) {
        return {
          success: false,
          error: `Template inválido: ${validation.errors.join(', ')}`
        };
      }
    }

    const supabase = await createClient();

    const { data: template, error } = await supabase
      .from('notification_templates')
      .update({
        ...validatedUpdates,
        updated_at: new Date().toISOString(),
        version: currentTemplate.data!.version + 1
      })
      .eq('id', templateId)
      .select()
      .single();

    if (error) {
      console.error('Erro ao atualizar template:', error);
      return {
        success: false,
        error: `Erro ao atualizar template: ${error.message}`
      };
    }

    return {
      success: true,
      data: template,
      message: 'Template atualizado com sucesso'
    };

  } catch (error) {
    console.error('Erro no serviço de templates:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Deleta um template (soft delete)
 */
export async function deleteTemplate(templateId: string): Promise<NotificationServiceResponse<void>> {
  try {
    const supabase = await createClient();

    const { error } = await supabase
      .from('notification_templates')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', templateId);

    if (error) {
      console.error('Erro ao deletar template:', error);
      return {
        success: false,
        error: `Erro ao deletar template: ${error.message}`
      };
    }

    return {
      success: true,
      message: 'Template deletado com sucesso'
    };

  } catch (error) {
    console.error('Erro no serviço de templates:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Busca variáveis disponíveis para um tipo de template
 */
export async function getAvailableVariables(
  templateType: NotificationType
): Promise<NotificationServiceResponse<TemplateVariable[]>> {
  try {
    const supabase = await createClient();

    const { data: variables, error } = await supabase
      .from('template_variables')
      .select('*')
      .eq('template_type', templateType)
      .order('category', { ascending: true })
      .order('variable_name', { ascending: true });

    if (error) {
      console.error('Erro ao buscar variáveis:', error);
      return {
        success: false,
        error: `Erro ao buscar variáveis: ${error.message}`
      };
    }

    return {
      success: true,
      data: variables || []
    };

  } catch (error) {
    console.error('Erro no serviço de templates:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Valida um template
 */
export async function validateTemplate(
  template: string,
  templateType: NotificationType
): Promise<NotificationServiceResponse<TemplateValidationResult>> {
  try {
    const availableVariables = await getAvailableVariables(templateType);
    if (!availableVariables.success) {
      return {
        success: false,
        error: availableVariables.error
      };
    }

    const validation = await TemplateValidator.validateTemplate(
      template,
      templateType,
      availableVariables.data!
    );

    return {
      success: true,
      data: validation
    };

  } catch (error) {
    console.error('Erro no serviço de templates:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Duplica um template existente
 */
export async function duplicateTemplate(
  templateId: string,
  newName: string
): Promise<NotificationServiceResponse<NotificationTemplate>> {
  try {
    // Buscar template original
    const originalTemplate = await getTemplate(templateId);
    if (!originalTemplate.success) {
      return originalTemplate;
    }

    const template = originalTemplate.data!;

    // Criar novo template baseado no original
    const newTemplateData: CreateTemplateData = {
      type: template.type,
      channel: template.channel,
      name: newName,
      subject_template: template.subject_template,
      body_template: template.body_template,
      parent_template_id: template.id
    };

    return await createTemplate(template.tenant_id!, newTemplateData);

  } catch (error) {
    console.error('Erro no serviço de templates:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Reseta template para versão padrão
 */
export async function resetToDefault(templateId: string): Promise<NotificationServiceResponse<NotificationTemplate>> {
  try {
    // Buscar template atual
    const currentTemplate = await getTemplate(templateId);
    if (!currentTemplate.success) {
      return currentTemplate;
    }

    const template = currentTemplate.data!;

    // Verificar se tem template pai (padrão)
    if (!template.parent_template_id) {
      return {
        success: false,
        error: 'Template não possui versão padrão para reset'
      };
    }

    // Buscar template padrão
    const defaultTemplate = await getTemplate(template.parent_template_id);
    if (!defaultTemplate.success) {
      return {
        success: false,
        error: 'Template padrão não encontrado'
      };
    }

    // Atualizar com dados do template padrão
    const updates: UpdateTemplateData = {
      subject_template: defaultTemplate.data!.subject_template,
      body_template: defaultTemplate.data!.body_template
    };

    return await updateTemplate(templateId, updates);

  } catch (error) {
    console.error('Erro no serviço de templates:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Cria variáveis de exemplo para um tipo de template
 */
export async function createExampleVariables(templateType: NotificationType): Promise<NotificationServiceResponse<Record<string, any>>> {
  try {
    const availableVariables = await getAvailableVariables(templateType);
    if (!availableVariables.success) {
      return availableVariables;
    }

    const examples = TemplateEngine.createExampleVariables(availableVariables.data!);

    return {
      success: true,
      data: examples
    };

  } catch (error) {
    console.error('Erro no serviço de templates:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}
