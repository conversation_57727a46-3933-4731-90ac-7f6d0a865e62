/**
 * Hook para gerenciamento de templates de notificação
 * Fornece operações CRUD e funcionalidades avançadas usando React Query
 */

'use client';

import { useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTenant } from '@/hooks/tenant';
import { CACHE_KEYS } from '@/constants/cache-keys';
import {
  getTemplatesByTenant,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  duplicateTemplate,
  validateTemplate,
  resetToDefault
} from '@/src/app/(dashboard)/academia/configuracoes/templates/actions/template-actions';
import type {
  NotificationTemplate,
  NotificationType,
  CreateTemplateData,
  UpdateTemplateData,
  TemplateValidationResult
} from '@/services/notifications/types/notification-types';

interface UseTemplateManagementReturn {
  // Estado
  templates: NotificationTemplate[];
  loading: boolean;
  error: string | null;

  // Operações CRUD
  loadTemplates: (type?: NotificationType) => Promise<void>;
  createTemplateMutation: {
    mutate: (data: CreateTemplateData) => void;
    isPending: boolean;
    error: Error | null;
  };
  updateTemplateMutation: {
    mutate: (variables: { id: string; data: UpdateTemplateData }) => void;
    isPending: boolean;
    error: Error | null;
  };
  deleteTemplateMutation: {
    mutate: (id: string) => void;
    isPending: boolean;
    error: Error | null;
  };
  duplicateTemplateMutation: {
    mutate: (variables: { id: string; newName: string }) => void;
    isPending: boolean;
    error: Error | null;
  };

  // Operações avançadas
  validateTemplateAsync: (template: string, type: NotificationType) => Promise<TemplateValidationResult | null>;
  resetToDefaultMutation: {
    mutate: (id: string) => void;
    isPending: boolean;
    error: Error | null;
  };

  // Utilitários
  refetch: () => void;
}

export function useTemplateManagement(): UseTemplateManagementReturn {
  const { tenantInfo } = useTenant();
  const queryClient = useQueryClient();

  // Query para buscar templates
  const {
    data: templates = [],
    isLoading: loading,
    error: queryError,
    refetch
  } = useQuery({
    queryKey: CACHE_KEYS.NOTIFICATION_TEMPLATES.LIST(tenantInfo?.id || '', undefined),
    queryFn: async () => {
      if (!tenantInfo?.id) return [];
      const result = await getTemplatesByTenant(tenantInfo.id);
      if (!result.success) {
        throw new Error(result.error || 'Erro ao carregar templates');
      }
      return result.data || [];
    },
    enabled: !!tenantInfo?.id
  });

  const error = queryError?.message || null;

  const loadTemplates = useCallback(async (type?: NotificationType) => {
    if (!tenantInfo?.id) return;

    // Invalidar e refetch com filtro de tipo
    await queryClient.invalidateQueries({
      queryKey: CACHE_KEYS.NOTIFICATION_TEMPLATES.LIST(tenantInfo.id, type)
    });
  }, [tenantInfo?.id, queryClient]);

  // Mutation para criar template
  const createTemplateMutation = useMutation({
    mutationFn: async (data: CreateTemplateData) => {
      if (!tenantInfo?.id) {
        throw new Error('Tenant não encontrado');
      }
      const result = await createTemplate(tenantInfo.id, data);
      if (!result.success) {
        throw new Error(result.error || 'Erro ao criar template');
      }
      return result.data!;
    },
    onSuccess: () => {
      // Invalidar cache para recarregar lista
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.NOTIFICATION_TEMPLATES.LIST(tenantInfo?.id || '', undefined)
      });
    }
  });

  // Mutation para atualizar template
  const updateTemplateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateTemplateData }) => {
      const result = await updateTemplate(id, data);
      if (!result.success) {
        throw new Error(result.error || 'Erro ao atualizar template');
      }
      return result.data!;
    },
    onSuccess: () => {
      // Invalidar cache para recarregar lista
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.NOTIFICATION_TEMPLATES.LIST(tenantInfo?.id || '', undefined)
      });
    }
  });

  // Mutation para deletar template
  const deleteTemplateMutation = useMutation({
    mutationFn: async (id: string) => {
      const result = await deleteTemplate(id);
      if (!result.success) {
        throw new Error(result.error || 'Erro ao deletar template');
      }
      return result;
    },
    onSuccess: () => {
      // Invalidar cache para recarregar lista
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.NOTIFICATION_TEMPLATES.LIST(tenantInfo?.id || '', undefined)
      });
    }
  });

  // Mutation para duplicar template
  const duplicateTemplateMutation = useMutation({
    mutationFn: async ({ id, newName }: { id: string; newName: string }) => {
      const result = await duplicateTemplate(id, newName);
      if (!result.success) {
        throw new Error(result.error || 'Erro ao duplicar template');
      }
      return result.data!;
    },
    onSuccess: () => {
      // Invalidar cache para recarregar lista
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.NOTIFICATION_TEMPLATES.LIST(tenantInfo?.id || '', undefined)
      });
    }
  });

  // Função para validar template (não precisa de mutation, é só validação)
  const validateTemplateAsync = useCallback(async (
    template: string,
    type: NotificationType
  ): Promise<TemplateValidationResult | null> => {
    try {
      const result = await validateTemplate(template, type);

      if (result.success) {
        return result.data!;
      } else {
        throw new Error(result.error || 'Erro ao validar template');
      }
    } catch (err) {
      console.error('Erro ao validar template:', err);
      return null;
    }
  }, []);

  // Mutation para resetar template para padrão
  const resetToDefaultMutation = useMutation({
    mutationFn: async (id: string) => {
      const result = await resetToDefault(id);
      if (!result.success) {
        throw new Error(result.error || 'Erro ao resetar template');
      }
      return result.data!;
    },
    onSuccess: () => {
      // Invalidar cache para recarregar lista
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.NOTIFICATION_TEMPLATES.LIST(tenantInfo?.id || '', undefined)
      });
    }
  });

  return {
    // Estado
    templates,
    loading,
    error,

    // Operações CRUD
    loadTemplates,
    createTemplateMutation,
    updateTemplateMutation,
    deleteTemplateMutation,
    duplicateTemplateMutation,

    // Operações avançadas
    validateTemplateAsync,
    resetToDefaultMutation,

    // Utilitários
    refetch
  };
}
